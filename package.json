{"name": "frontpfe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@azure/msal-browser": "^4.5.1", "@chakra-ui/react": "^3.16.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@google-pay/button-react": "^3.1.0", "@headlessui/react": "^2.2.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^5.11.15", "@paypal/react-paypal-js": "^8.8.3", "@react-oauth/google": "^0.12.1", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.2.0", "@stripe/stripe-react-native": "^0.45.0", "@types/jwt-decode": "^3.1.0", "@types/react-datepicker": "^7.0.0", "axios": "^1.8.4", "bootstrap": "^5.3.3", "dotenv": "^16.5.0", "framer-motion": "^12.7.4", "jquery": "^3.7.1", "jwt-decode": "^4.0.0", "lodash.debounce": "^4.0.8", "lucide-react": "^0.510.0", "owl.carousel": "^2.3.4", "react": "^19.0.0", "react-bootstrap": "^2.10.9", "react-country-flag": "^3.1.0", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-google-recaptcha": "^3.1.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-owl-carousel": "^2.3.3", "react-phone-input-2": "^2.15.1", "react-qr-code": "^2.0.15", "react-query": "^3.39.3", "react-router-dom": "^7.4.0", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "webfontloader": "^1.6.28"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/jquery": "^3.5.32", "@types/lodash.debounce": "^4.0.9", "@types/node": "^22.14.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-google-recaptcha": "^2.1.9", "@types/react-helmet": "^6.1.11", "@types/webfontloader": "^1.6.38", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.15", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.3", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}