import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import {
  FaTrash,
  FaEdit,
  FaChartLine,
  FaEllipsisV,
  FaEye,
  FaEyeSlash,
  FaLock,
  FaExternalLinkAlt,
  FaIdCard
} from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { Pixel } from '../services/Pixel';

interface PixelItemProps {
  pixel: Pixel;
  onDelete: (id: string) => void;
  vcardName?: string;
}

const PixelItem: React.FC<PixelItemProps> = ({ pixel, onDelete, vcardName }) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleEditClick = () => {
    navigate(`/admin/pixel/edit/${pixel.id}`);
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDropdown(false);
    if (window.confirm('Are you sure you want to delete this pixel?')) {
      setIsDeleting(true);
      onDelete(pixel.id);
      setIsDeleting(false);
    }
  };

  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDropdown(!showDropdown);
  };

  const getStatusColor = () => {
    return pixel.is_active 
      ? 'bg-green-400/20 text-green-600 dark:text-green-400' 
      : 'bg-red-400/20 text-red-600 dark:text-red-400';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0 }}
      whileHover={{ scale: 1.02 }}
      className="relative group bg-white dark:bg-gray-800 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100/50 dark:border-gray-700/50 cursor-pointer backdrop-blur-sm bg-opacity-50"
    >
      {pixel.isDisabled && (
        <div className="absolute inset-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm flex items-center justify-center rounded-2xl z-10">
          <div className="text-center p-4">
            <div className="animate-pulse">
              <FaLock className="text-gray-600 dark:text-gray-300 text-2xl mb-2 mx-auto" />
            </div>
            <div className="text-gray-600 dark:text-gray-300 text-sm font-medium mb-2">
              Upgrade plan to activate
            </div>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate('/admin/account/plan')}
              className="px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white rounded-lg text-sm font-medium transition-all shadow-lg"
            >
              Upgrade Now
            </motion.button>
          </div>
        </div>
      )}

      <div className="p-5 h-full flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-start mb-4">
          <div className={`flex items-center px-3 py-1 rounded-full ${getStatusColor()} text-xs font-medium space-x-1.5`}>
            <div className={`w-2 h-2 rounded-full ${pixel.is_active ? 'bg-green-500' : 'bg-red-500'} animate-pulse`} />
            <span>{pixel.is_active ? 'Active' : 'Inactive'}</span>
          </div>
          
          <div className="relative" ref={dropdownRef}>
            <motion.button
              whileHover={{ rotate: 90 }}
              onClick={toggleDropdown}
              className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg text-gray-500 dark:text-gray-400 transition-colors"
              disabled={isDeleting}
            >
              <FaEllipsisV className="w-4 h-4" />
            </motion.button>

            {showDropdown && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="absolute right-0 top-8 w-44 bg-white dark:bg-gray-700 rounded-xl shadow-2xl border border-gray-100/50 dark:border-gray-600/50 backdrop-blur-sm"
              >
                <button
                  onClick={handleEditClick}
                  className="flex items-center w-full px-4 py-3 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50/50 dark:hover:bg-gray-600/50 transition-colors first:rounded-t-xl last:rounded-b-xl"
                >
                  <FaEdit className="mr-3 text-gray-500 dark:text-gray-400" />
                  Edit
                </button>
                <button
                  onClick={handleDeleteClick}
                  disabled={isDeleting}
                  className="flex items-center w-full px-4 py-3 text-sm text-red-600 hover:bg-gray-50/50 dark:hover:bg-gray-600/50 transition-colors"
                >
                  <FaTrash className="mr-3" />
                  Delete
                </button>
              </motion.div>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="flex flex-col flex-1">
          <div className="mb-5 flex items-center justify-center relative">
            <div className="w-16 h-16 rounded-2xl bg-purple-500/10 flex items-center justify-center relative group-hover:bg-purple-500/20 transition-colors">
              <motion.div
                whileHover={{ rotate: -15, scale: 1.1 }}
                className="bg-gradient-to-br from-purple-500 to-blue-500 p-3 rounded-xl shadow-lg"
              >
                <FaChartLine className="text-2xl text-white" />
              </motion.div>
              
              {vcardName && (
                <div className="absolute -bottom-3 px-3 py-1.5 bg-white dark:bg-gray-700 rounded-full shadow-lg flex items-center border border-purple-100/50 dark:border-gray-600/50 backdrop-blur-sm">
                  <FaIdCard className="mr-2 text-purple-500 dark:text-purple-400" />
                  <span className="text-xs font-medium text-gray-700 dark:text-gray-200 truncate max-w-[120px]">
                    {vcardName}
                  </span>
                </div>
              )}
            </div>
          </div>

          <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-3 text-center line-clamp-2 leading-tight">
            {pixel.name || 'Unnamed Pixel'}
          </h3>

          {vcardName && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="mb-4 px-4 py-3 bg-gradient-to-r from-purple-50/50 to-blue-50/50 dark:from-gray-700 dark:to-gray-600 rounded-xl flex items-center space-x-3 border border-purple-100/50 dark:border-gray-600/50 backdrop-blur-sm"
            >
              <div className="bg-purple-500/10 p-2 rounded-lg">
                <FaIdCard className="text-purple-500 dark:text-purple-400 text-lg" />
              </div>
              <span 
                className="text-sm text-gray-700 dark:text-gray-200 truncate flex-1"
                title={`Linked to: ${vcardName}`}
              >
                Linked to <span className="font-semibold text-purple-600 dark:text-purple-400 ml-1.5">{vcardName}</span>
              </span>
            </motion.div>
          )}
        </div>

        {/* Footer */}
        <div className="mt-auto pt-4 border-t border-gray-100/50 dark:border-gray-700/50">
          <div className="flex justify-between items-center text-xs">
            <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
              <FaExternalLinkAlt className="text-gray-400 dark:text-gray-500" />
              <span className="font-medium">Tracking Analytics</span>
            </div>
            <span className="text-gray-400 dark:text-gray-500 font-mono">
              {new Date(pixel.created_at).toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'short',
                year: 'numeric'
              })}
            </span>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default PixelItem;