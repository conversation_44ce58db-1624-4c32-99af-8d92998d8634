import React from 'react';
import { motion } from 'framer-motion';
import { FaLink, FaTrash, FaEdit, FaChartLine } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { Pixel } from '../services/Pixel';

interface PixelItemProps {
  pixel: Pixel;
  onDelete: (id: string) => void;
}

const PixelItem: React.FC<PixelItemProps> = ({ pixel, onDelete }) => {
  const handleCopyLink = () => {
    navigator.clipboard.writeText(pixel.url);
    toast.success('Tracking URL copied to clipboard!');
  };

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this pixel?')) {
      onDelete(pixel.id);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0 }}
      className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 shadow-sm hover:shadow-md transition-shadow"
    >
      <div className="flex justify-between items-start mb-3">
        <div className="flex-1">
          <h3 className="font-semibold text-gray-800 dark:text-gray-100 truncate">
            {pixel.name}
          </h3>
          <button
            onClick={handleCopyLink}
            className="flex items-center text-sm text-purple-600 dark:text-purple-400 hover:text-purple-700 mt-1"
          >
            <FaLink className="mr-2" />
            <span className="truncate">{pixel.url}</span>
          </button>
        </div>
        <span className={`badge ${pixel.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          {pixel.is_active ? 'Active' : 'Inactive'}
        </span>
      </div>

      <div className="flex justify-between items-center text-sm text-gray-500 dark:text-gray-400">
        <div className="flex items-center gap-2">
          <FaChartLine />
          <span>Tracking</span>
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => {/* Implement edit functionality */}}
            className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
          >
            <FaEdit className="text-blue-500" />
          </button>
          <button
            onClick={handleDelete}
            className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
          >
            <FaTrash className="text-red-500" />
          </button>
        </div>
      </div>

      <div className="mt-3 text-xs text-gray-400">
        Created: {new Date(pixel.created_at).toLocaleDateString()}
      </div>
    </motion.div>
  );
};

export default PixelItem;