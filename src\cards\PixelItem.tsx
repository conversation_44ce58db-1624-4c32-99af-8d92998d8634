import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import {
  FaLink,
  FaTrash,
  FaEdit,
  FaChartLine,
  FaEllipsisV,
  FaEye,
  FaEyeSlash,
  FaLock,
  FaExternalLinkAlt
} from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Pixel } from '../services/Pixel';

interface PixelItemProps {
  pixel: Pixel;
  onDelete: (id: string) => void;
}

const PixelItem: React.FC<PixelItemProps> = ({ pixel, onDelete }) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleCopyLink = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigator.clipboard.writeText(pixel.url);
    toast.success('Tracking URL copied to clipboard!');
  };

  const handleEditClick = () => {
    navigate(`/pixels/edit/${pixel.id}`);
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDropdown(false);
    if (window.confirm('Are you sure you want to delete this pixel?')) {
      setIsDeleting(true);
      onDelete(pixel.id);
      setIsDeleting(false);
    }
  };

  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDropdown(!showDropdown);
  };

  const handleDoubleClick = () => {
    navigate(`/pixels/${pixel.id}`);
  };

  // Generate a gradient background based on the pixel name
  const getBackgroundColor = () => {
    // Simple hash function to generate a color from the pixel name
    const hash = pixel.name.split('').reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);

    const h = Math.abs(hash % 360);
    const s = 70 + (hash % 20); // 70-90%
    const l = 60 + (hash % 15); // 60-75%

    return `hsl(${h}, ${s}%, ${l}%)`;
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0 }}
        className="bg-white dark:bg-gray-700 relative rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 h-72 w-64 cursor-pointer"
        style={{ background: `linear-gradient(135deg, ${getBackgroundColor()}, ${getBackgroundColor()}dd)` }}
        onDoubleClick={handleDoubleClick}
      >
        {pixel.isDisabled && (
          <div className="absolute inset-0 bg-black/30 dark:bg-gray-900/50 flex items-center justify-center rounded-xl z-10">
            <div className="text-center p-4">
              <FaLock className="text-white text-2xl mb-2 mx-auto" />
              <div className="text-white text-sm font-medium mb-2">
                Upgrade plan to activate
              </div>
              <button
                onClick={() => navigate('/account/plan')}
                className="px-3 py-1 bg-purple-500 hover:bg-purple-600 text-white rounded-lg text-xs transition-colors"
              >
                Upgrade Now
              </button>
            </div>
          </div>
        )}

        <div className="relative h-full flex flex-col p-6">
          <div className="flex justify-center mb-4">
            <div
              className="w-16 h-16 rounded-full bg-white/20 flex items-center justify-center border-2 border-white"
            >
              <FaChartLine className="text-white text-xl" />
            </div>
          </div>

          <div className="flex-1 flex flex-col items-center text-center">
            <h2 className="text-xl font-bold mb-2 line-clamp-1 text-white">
              {pixel.name}
            </h2>
            <button
              onClick={handleCopyLink}
              className="flex items-center text-sm text-white/90 hover:text-white mt-1 mb-3 px-3 py-1 bg-white/20 rounded-full"
            >
              <FaLink className="mr-2" />
              <span className="truncate max-w-[150px]">{pixel.url.replace(/^https?:\/\//, '')}</span>
            </button>
          </div>

          <div className="flex justify-between items-center pt-3 border-t border-white/20">
            <div className="flex items-center space-x-2">
              <span
                className={`px-2 py-1 rounded-full text-xs text-white flex items-center ${
                  pixel.is_active ? 'bg-green-500/80' : 'bg-gray-500/80'
                }`}
              >
                {pixel.is_active ? (
                  <>
                    <FaEye className="mr-1" /> Active
                  </>
                ) : (
                  <>
                    <FaEyeSlash className="mr-1" /> Inactive
                  </>
                )}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={handleCopyLink}
                className="p-1.5 rounded-full hover:bg-white/20 transition text-white"
                title="Copy tracking URL"
              >
                <FaExternalLinkAlt className="w-3 h-3" />
              </button>

              <div className="relative" ref={dropdownRef}>
                <button
                  onClick={toggleDropdown}
                  className="p-1 rounded-full hover:bg-white/20 transition text-white"
                  disabled={isDeleting}
                >
                  <FaEllipsisV size={14} />
                </button>

                {showDropdown && (
                  <div className="absolute right-0 bottom-full mb-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-xl z-[1000] border border-gray-200 dark:border-gray-700">
                    <button
                      onClick={handleEditClick}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <FaEdit className="mr-2" /> Edit
                    </button>
                    <button
                      onClick={handleDeleteClick}
                      disabled={isDeleting}
                      className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50"
                    >
                      <FaTrash className="mr-2" /> Delete
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="absolute bottom-2 right-2 text-xs text-white/70">
            {new Date(pixel.created_at).toLocaleDateString()}
          </div>
        </div>
      </motion.div>
    </>
  );
};

export default PixelItem;