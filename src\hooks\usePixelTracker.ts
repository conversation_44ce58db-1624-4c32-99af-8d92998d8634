import { useCallback, useEffect, useRef } from 'react';
import { pixelService } from '../services/api';
import { PixelEventParams } from '../services/Pixel';

const usePixelTracker = (pixelId: string | null, active: boolean) => {
  const scrollDepth = useRef<number>(0);
  const mouseMoveTracker = useRef<NodeJS.Timeout | null>(null);
  const heartbeatTimer = useRef<NodeJS.Timeout | null>(null);
  const isActiveRef = useRef(active);
  
  // Mettre à jour la ref lorsque active change
  useEffect(() => {
    isActiveRef.current = active;
  }, [active]);

  const trackEvent = useCallback((params: PixelEventParams) => {
    if (!isActiveRef.current || !pixelId) return;
    
    // Normaliser les paramètres pour l'API
    const normalizedParams: Record<string, string> = {
      eventType: params.eventType,
    };

    // Ajouter les autres paramètres s'ils existent
    if (params.blockId) normalizedParams.blockId = String(params.blockId);
    if (params.duration) normalizedParams.duration = String(params.duration);
    
    // Ajouter les métadonnées si elles existent
    if (params.metadata) {
      try {
        normalizedParams.metadata = JSON.stringify(params.metadata);
      } catch (e) {
        console.error('Failed to stringify metadata', e);
      }
    }

    pixelService.trackEvent(pixelId, normalizedParams);
  }, [pixelId]);

  const trackScroll = useCallback(() => {
    const scrollPosition = window.scrollY;
    const documentHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
    const scrollPercentage = Math.floor((scrollPosition / documentHeight) * 100);
    
    if (scrollPercentage > scrollDepth.current) {
      scrollDepth.current = scrollPercentage;
      trackEvent({ 
        eventType: 'scroll', 
        metadata: { depth: scrollPercentage } 
      });
    }
  }, [trackEvent]);

  const trackMouseMove = useCallback((e: MouseEvent) => {
    if (mouseMoveTracker.current) clearTimeout(mouseMoveTracker.current);
    
    mouseMoveTracker.current = setTimeout(() => {
      trackEvent({ 
        eventType: 'mouse_move',
        metadata: { 
          x: e.clientX, 
          y: e.clientY 
        }
      });
    }, 1000);
  }, [trackEvent]);

  const startHeartbeat = useCallback(() => {
    // Envoyer le premier heartbeat immédiatement
    trackEvent({ eventType: 'heartbeat' });
    
    // Puis configurer l'intervalle
    heartbeatTimer.current = setInterval(() => {
      trackEvent({ eventType: 'heartbeat' });
    }, 30000);
  }, [trackEvent]);

  useEffect(() => {
    if (!active || !pixelId) return;

    // Tracking de la vue initiale
    trackEvent({ eventType: 'view' });
    
    // Démarrer le heartbeat
    startHeartbeat();

    // Configurer les écouteurs d'événements
    window.addEventListener('scroll', trackScroll);
    document.addEventListener('mousemove', trackMouseMove);

    return () => {
      // Nettoyer les écouteurs d'événements
      window.removeEventListener('scroll', trackScroll);
      document.removeEventListener('mousemove', trackMouseMove);
      
      // Arrêter le heartbeat
      if (heartbeatTimer.current) {
        clearInterval(heartbeatTimer.current);
      }
      
      // Annuler le timer de mouvement de souris
      if (mouseMoveTracker.current) {
        clearTimeout(mouseMoveTracker.current);
      }
    };
  }, [active, pixelId, trackEvent, trackScroll, trackMouseMove, startHeartbeat]);

  return { trackEvent };
};

export default usePixelTracker;