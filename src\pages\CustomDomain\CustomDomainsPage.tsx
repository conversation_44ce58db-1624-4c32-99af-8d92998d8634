import React, { useState, useEffect, useRef } from 'react';
import {
  FaPlus,
  FaTrash,
  FaSync,
  FaLink,
  FaUnlink,
  FaEdit,
  FaSearch,
  FaTimes,
  FaAngleLeft,
  FaAngleRight,
  FaGlobe,
  FaExclamationTriangle,
  FaCheckCircle,
  FaPauseCircle
} from 'react-icons/fa';
import { Link, useNavigate } from 'react-router-dom';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { customDomainService } from '../../services/api';
import { CustomDomain } from '../../services/CustomDomain';
import { motion, AnimatePresence } from 'framer-motion';

const LoadingSpinner: React.FC = () => (
  <div className="flex justify-center items-center min-h-[50vh] py-12">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
  </div>
);

interface EmptyStateProps {
  title: string;
  description: string;
  actionText?: string;
  actionLink?: string;
  icon?: React.ReactNode;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  actionText,
  actionLink,
  icon
}) => (
  <div className="text-center py-12 px-4 rounded-xl bg-white dark:bg-gray-800 shadow-lg">
    <div className="mb-4 flex justify-center text-purple-500 dark:text-purple-400 text-4xl">
      {icon || <FaGlobe />}
    </div>
    <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">{title}</h3>
    <p className="text-gray-500 dark:text-gray-300 max-w-md mx-auto mb-6">{description}</p>
    {actionText && actionLink && (
      <Link
        to={actionLink}
        className="inline-flex items-center px-5 py-2.5 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 transition-colors shadow-md"
      >
        <FaPlus className="mr-2" />
        {actionText}
      </Link>
    )}
  </div>
);

const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
  const statusConfig = {
    pending: {
      bg: 'bg-yellow-100 dark:bg-yellow-900/30',
      text: 'text-yellow-800 dark:text-yellow-300',
      icon: <FaExclamationTriangle className="mr-1.5" />,
      label: 'Pending Verification'
    },
    active: {
      bg: 'bg-green-100 dark:bg-green-900/30',
      text: 'text-green-800 dark:text-green-300',
      icon: <FaCheckCircle className="mr-1.5" />,
      label: 'Active'
    },
    failed: {
      bg: 'bg-red-100 dark:bg-red-900/30',
      text: 'text-red-800 dark:text-red-300',
      icon: <FaExclamationTriangle className="mr-1.5" />,
      label: 'Verification Failed'
    },
    disabled: {
      bg: 'bg-gray-100 dark:bg-gray-700/50',
      text: 'text-gray-800 dark:text-gray-300',
      icon: <FaPauseCircle className="mr-1.5" />,
      label: 'Disabled'
    }
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

  return (
    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
      {config.icon}
      {config.label}
    </span>
  );
};

const CustomDomainCard: React.FC<{ 
  domain: CustomDomain; 
  onDelete: (id: number) => void;
  onVerify: (id: number) => void;
}> = ({ domain, onDelete, onVerify }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-all hover:shadow-md"
    >
      <div className="p-5">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-semibold text-lg text-gray-800 dark:text-white flex items-center gap-2">
              {domain.domain}
              <StatusBadge status={domain.status} />
            </h3>
            
            <div className="mt-2 text-sm text-gray-600 dark:text-gray-300">
              {domain.custom_index_url && (
                <a 
                  href={domain.custom_index_url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-purple-600 dark:text-purple-400 hover:underline"
                >
                  Landing Page
                </a>
              )}
            </div>
          </div>
        </div>

        <div className="mt-4">
          {domain.vcard ? (
            <div className="flex items-center gap-2 text-sm">
              <span className="text-gray-500 dark:text-gray-400">Linked to:</span>
              <Link 
                to={`/admin/vcard/${domain.vcard.id}`}
                className="font-medium text-purple-600 dark:text-purple-400 hover:underline"
              >
                {domain.vcard.name}
              </Link>
            </div>
          ) : (
            <div className="text-sm text-gray-500 dark:text-gray-400 italic">
              No vCard linked
            </div>
          )}
        </div>

        <div className="mt-5 flex flex-wrap gap-2">
          {domain.status === 'pending' && (
            <button
              onClick={() => domain.id && onVerify(domain.id)}
              className="flex items-center px-3 py-1.5 text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors"
            >
              <FaSync className="mr-1.5" />
              Verify
            </button>
          )}
          
          <button
            onClick={() => domain.id && navigate(`/admin/custom-domains/edit/${domain.id}`)}
            className="flex items-center px-3 py-1.5 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            <FaEdit className="mr-1.5" />
            Edit
          </button>
          
          {domain.vcardId ? (
            <button
              onClick={() => domain.id && customDomainService.unlinkFromVCard(domain.id).then(fetchCustomDomains)}
              className="flex items-center px-3 py-1.5 text-sm bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-lg hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors"
            >
              <FaUnlink className="mr-1.5" />
              Unlink
            </button>
          ) : (
            <button
              onClick={() => navigate(`/admin/custom-domains/link/${domain.id}`)}
              className="flex items-center px-3 py-1.5 text-sm bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-800/50 transition-colors"
            >
              <FaLink className="mr-1.5" />
              Link vCard
            </button>
          )}
          
          <button
            onClick={() => domain.id && onDelete(domain.id)}
            className="flex items-center px-3 py-1.5 text-sm bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-lg hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors"
          >
            <FaTrash className="mr-1.5" />
            Delete
          </button>
        </div>
      </div>
    </motion.div>
  );
};

const CustomDomainsPage: React.FC = () => {
  const [domains, setDomains] = useState<CustomDomain[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredDomains, setFilteredDomains] = useState<CustomDomain[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const navigate = useNavigate();
  const domainsPerPage = 12;
  const filterButtonRef = useRef<HTMLDivElement>(null);
  const [showFilters, setShowFilters] = useState(false);

  const statusOptions = [
    { value: 'all', label: 'All Statuses' },
    { value: 'pending', label: 'Pending' },
    { value: 'active', label: 'Active' },
    { value: 'failed', label: 'Failed' },
    { value: 'disabled', label: 'Disabled' }
  ];

  const fetchCustomDomains = async () => {
    try {
      setLoading(true);
      const response = await customDomainService.getUserDomains();

      let domainsData: CustomDomain[] = [];
      if (Array.isArray(response)) {
        domainsData = response;
      } else if (response && Array.isArray(response.data)) {
        domainsData = response.data;
      } else {
        console.warn('Unexpected domains response format:', response);
        domainsData = [];
      }

      setDomains(domainsData);
    } catch (error) {
      console.error('Error fetching custom domains:', error);
      toast.error('Error loading domains');
      setDomains([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCustomDomains();
  }, []);

  useEffect(() => {
    let result = domains;
    
    if (searchTerm) {
      result = result.filter(domain =>
        domain.domain.toLowerCase().includes(searchTerm.toLowerCase())
    }
    
    if (statusFilter !== 'all') {
      result = result.filter(domain => domain.status === statusFilter);
    }
    
    setFilteredDomains(result);
    setCurrentPage(1);
  }, [domains, searchTerm, statusFilter]);

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this domain?')) {
      try {
        await customDomainService.delete(id);
        toast.success('Domain deleted successfully');
        fetchCustomDomains();
      } catch (error) {
        console.error('Error deleting domain:', error);
        toast.error('Failed to delete domain');
      }
    }
  };

  const handleVerify = async (id: number) => {
    try {
      const response = await customDomainService.verify(id);
      toast.success(response.message || 'Domain verified successfully');
      fetchCustomDomains();
    } catch (error: any) {
      console.error('Error verifying domain:', error);
      toast.error(error.response?.data?.message || 'Failed to verify domain');
    }
  };

  const indexOfLastDomain = currentPage * domainsPerPage;
  const indexOfFirstDomain = indexOfLastDomain - domainsPerPage;
  const currentDomains = filteredDomains.slice(indexOfFirstDomain, indexOfLastDomain);
  const totalPages = Math.ceil(filteredDomains.length / domainsPerPage);

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  const hasActiveFilters = searchTerm || statusFilter !== 'all';

  return (
    <div className="p-4 sm:p-6 lg:px-8 max-w-7xl mx-auto">
      <ToastContainer position="top-right" autoClose={5000} />
      
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white">Custom Domains</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your custom domains and associations
          </p>
        </div>
        
        <button
          onClick={() => navigate('/admin/custom-domains/create')}
          className="flex items-center bg-purple-600 hover:bg-purple-700 text-white font-medium py-2.5 px-5 rounded-lg transition-colors shadow-md"
        >
          <FaPlus className="mr-2" />
          Add Domain
        </button>
      </div>

      <div className="mb-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaSearch className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search domains..."
              className="w-full pl-10 pr-4 py-2.5 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div className="relative" ref={filterButtonRef}>
            <button
              className={`w-full md:w-auto flex items-center justify-between px-4 py-2.5 bg-white dark:bg-gray-800 border ${
                hasActiveFilters 
                  ? 'border-purple-500' 
                  : 'border-gray-300 dark:border-gray-600'
              } rounded-lg`}
              onClick={() => setShowFilters(!showFilters)}
            >
              <span>Filters</span>
              <FaFilter className={`ml-2 ${hasActiveFilters ? 'text-purple-500' : 'text-gray-400'}`} />
            </button>
            
            {showFilters && (
              <div className="absolute z-10 mt-2 w-full md:w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
                <div className="p-4">
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Status
                    </label>
                    <select
                      className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                    >
                      {statusOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="flex justify-between">
                    <button
                      onClick={() => {
                        setSearchTerm('');
                        setStatusFilter('all');
                      }}
                      className="text-sm text-purple-600 dark:text-purple-400 hover:underline"
                    >
                      Reset filters
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {hasActiveFilters && (
        <div className="mb-4 flex items-center gap-2 flex-wrap">
          <span className="text-sm text-gray-500 dark:text-gray-400">Active filters:</span>
          {statusFilter !== 'all' && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">
              Status: {statusOptions.find(o => o.value === statusFilter)?.label}
            </span>
          )}
          {searchTerm && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
              Search: "{searchTerm}"
            </span>
          )}
          <button
            onClick={() => {
              setSearchTerm('');
              setStatusFilter('all');
            }}
            className="text-xs text-red-500 hover:text-red-700 dark:hover:text-red-400 flex items-center"
          >
            <FaTimes className="mr-1" /> Clear all
          </button>
        </div>
      )}

      {loading ? (
        <LoadingSpinner />
      ) : filteredDomains.length > 0 ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5 mb-6">
            <AnimatePresence>
              {currentDomains.map((domain) => (
                <CustomDomainCard 
                  key={domain.id} 
                  domain={domain}
                  onDelete={handleDelete}
                  onVerify={handleVerify}
                />
              ))}
            </AnimatePresence>
          </div>

          {totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <nav className="flex items-center gap-1">
                <button
                  onClick={() => paginate(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className={`p-2 rounded-md flex items-center justify-center ${
                    currentPage === 1
                      ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-600'
                  }`}
                >
                  <FaAngleLeft className="h-4 w-4" />
                </button>

                <div className="flex items-center gap-1 mx-2">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((number) => (
                    <button
                      key={number}
                      onClick={() => paginate(number)}
                      className={`w-8 h-8 text-sm rounded-md flex items-center justify-center ${
                        currentPage === number
                          ? 'bg-purple-500 text-white font-medium'
                          : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-600'
                      }`}
                    >
                      {number}
                    </button>
                  ))}
                </div>

                <button
                  onClick={() => paginate(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className={`p-2 rounded-md flex items-center justify-center ${
                    currentPage === totalPages
                      ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-600'
                  }`}
                >
                  <FaAngleRight className="h-4 w-4" />
                </button>
              </nav>
            </div>
          )}
        </>
      ) : (
        <EmptyState
          title={hasActiveFilters 
            ? "No domains match your criteria" 
            : "No custom domains yet"}
          description={hasActiveFilters 
            ? "Try adjusting your search or filters" 
            : "Get started by adding your first custom domain"}
          actionText="Add Domain"
          actionLink="/admin/custom-domains/create"
          icon={<FaGlobe />}
        />
      )}
    </div>
  );
};

export default CustomDomainsPage;