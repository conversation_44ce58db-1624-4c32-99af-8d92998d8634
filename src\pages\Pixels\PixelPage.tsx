import React, { useState, useEffect, useRef } from 'react';
import {
  FaPlus,
  FaFilter,
  FaAngleLeft,
  FaAngleRight,
  FaTimes,
  FaChartLine
} from 'react-icons/fa';
import { FiChevronRight, FiSearch } from 'react-icons/fi';
import { Link, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Breadcrumb } from 'react-bootstrap';
import PixelItem from '../../cards/PixelItem';
import EmptyState from '../../cards/EmptyState';
import LoadingSpinner from '../../Loading/LoadingSpinner';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { pixelService, limitService } from '../../services/api';
import { Pixel } from '../../services/Pixel';

const PixelPage: React.FC = () => {
  const [pixels, setPixels] = useState<Pixel[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredPixels, setFilteredPixels] = useState<Pixel[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const navigate = useNavigate();
  const [currentPlanLimit, setCurrentPlanLimit] = useState(1);
  const [activeFilters, setActiveFilters] = useState({
    status: 'all',
    dateRange: {
      start: undefined as Date | undefined,
      end: undefined as Date | undefined
    }
  });
  const cardsPerPage = 12;

  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) setCurrentUser(JSON.parse(userData));
  }, []);

  useEffect(() => {
    const fetchPlanLimit = async () => {
      try {
        const { max } = await limitService.checkVcardLimit();
        setCurrentPlanLimit(max === -1 ? Infinity : max);
      } catch (error) {
        console.error('Error fetching plan limits:', error);
      }
    };
    fetchPlanLimit();
  }, []);

  const fetchPixels = async () => {
    if (!currentUser?.id) return;

    try {
      setLoading(true);
      const response = await pixelService.getUserPixels();
      
      const formattedPixels = response.pixels.map((pixel: Pixel, index: number) => ({
        ...pixel,
        isDisabled: currentPlanLimit !== Infinity && index >= currentPlanLimit
      }));

      setPixels(formattedPixels);
      setFilteredPixels(formattedPixels);
    } catch (err) {
      console.error('Error fetching pixels:', err);
      toast.error('Failed to load pixels');
      setPixels([]);
      setFilteredPixels([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => { 
    fetchPixels(); 
  }, [currentUser, refreshTrigger, currentPlanLimit]);

  const applyFilters = () => {
    let filtered = [...pixels];

    // Search filter
    filtered = filtered.filter(pixel =>
      pixel.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pixel.url.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Status filter
    if (activeFilters.status !== 'all') {
      filtered = filtered.filter(pixel => 
        pixel.is_active === (activeFilters.status === 'active')
      );
    }

    // Date range filter
    if (activeFilters.dateRange.start || activeFilters.dateRange.end) {
      filtered = filtered.filter(pixel => {
        const pixelDate = new Date(pixel.created_at);
        const start = activeFilters.dateRange.start || new Date(0);
        const end = activeFilters.dateRange.end || new Date();

        return pixelDate >= start && pixelDate <= end;
      });
    }

    setFilteredPixels(filtered);
    setCurrentPage(1);
  };

  useEffect(() => { 
    applyFilters(); 
  }, [searchTerm, pixels, activeFilters]);

  const handleFilterChange = (filterType: string, value: any) => {
    setActiveFilters(prev => ({ ...prev, [filterType]: value }));
  };

  const resetFilters = () => {
    setActiveFilters({
      status: 'all',
      dateRange: { start: undefined, end: undefined }
    });
    setSearchTerm('');
  };

  const hasActiveFilters = () => {
    return activeFilters.status !== 'all' || 
           activeFilters.dateRange.start !== undefined ||
           activeFilters.dateRange.end !== undefined;
  };

  // Pagination
  const indexOfLastCard = currentPage * cardsPerPage;
  const indexOfFirstCard = indexOfLastCard - cardsPerPage;
  const currentCards = filteredPixels.slice(indexOfFirstCard, indexOfLastCard);
  const totalPages = Math.ceil(filteredPixels.length / cardsPerPage);
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  const handleCreatePixel = async () => {
    try {
      const { current, max } = await limitService.checkVcardLimit();
      if (max !== -1 && current >= max) {
        toast.warning(`You've reached the maximum of ${max} Pixels. Upgrade your plan.`);
      } else {
        navigate('/pixels/create');
      }
    } catch (error) {
      toast.error('Error checking plan limits');
    }
  };

  const handleDeletePixel = async (pixelId: string) => {
    try {
      await pixelService.delete(pixelId);
      toast.success('Pixel deleted successfully');
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      toast.error('Failed to delete pixel');
    }
  };

  if (loading) return <LoadingSpinner />;

  return (
    <div className="p-4 sm:p-6 lg:px-8 xl:px-28 w-full max-w-[90rem] mx-auto">
      <ToastContainer position="top-right" autoClose={5000} theme="colored" />

      <Breadcrumb className="mb-4 sm:mb-6">
        <Breadcrumb.Item linkAs={Link} linkProps={{ to: "/pixels" }} className="text-primary">
          Tracking Pixels
        </Breadcrumb.Item>
      </Breadcrumb>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 sm:mb-8 gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white">Pixels Manager</h1>
          <p className="text-primary mt-2 text-sm">Track user interactions and analytics</p>
        </div>

        <div className="w-full md:w-auto flex items-center gap-4">
          <div className="relative flex-1">
            <FiSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search pixels..."
              className="w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 focus:ring-2 focus:ring-purple-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <button
            className="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg border border-purple-500 hover:bg-gray-200 dark:hover:bg-gray-600"
            onClick={() => setShowFilterMenu(!showFilterMenu)}
          >
            <FaFilter className="text-purple-500" />
          </button>

          <button
            className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg flex items-center gap-2"
            onClick={handleCreatePixel}
          >
            <FaPlus /> New Pixel
          </button>
        </div>
      </div>

      {hasActiveFilters() && (
        <div className="mb-4 flex items-center gap-2 flex-wrap">
          <span className="text-sm text-gray-500">Active filters:</span>
          {activeFilters.status !== 'all' && (
            <span className="badge bg-blue-100 text-blue-800">
              Status: {activeFilters.status}
            </span>
          )}
          <button
            onClick={resetFilters}
            className="text-sm text-red-500 hover:text-red-700 flex items-center"
          >
            <FaTimes className="mr-1" /> Clear filters
          </button>
        </div>
      )}

      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
      >
        <AnimatePresence>
          {currentCards.map(pixel => (
            <PixelItem
              key={pixel.id}
              pixel={pixel}
              onDelete={() => handleDeletePixel(pixel.id)}
            />
          ))}
        </AnimatePresence>
      </motion.div>

      {filteredPixels.length === 0 && (
        <EmptyState
          title={searchTerm ? "No pixels found" : "No pixels created yet"}
          description={searchTerm ? "Try adjusting your search" : "Start tracking user interactions"}
          actionText="Create Pixel"
          actionLink="/pixels/create"
          icon={<FaChartLine size={40} />}
        />
      )}

      {totalPages > 1 && (
        <div className="mt-8 flex justify-center">
          <nav className="flex gap-2">
            <button
              onClick={() => paginate(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="p-2 disabled:opacity-50"
            >
              <FaAngleLeft />
            </button>
            
            {Array.from({ length: totalPages }, (_, i) => (
              <button
                key={i + 1}
                onClick={() => paginate(i + 1)}
                className={`w-8 h-8 rounded ${currentPage === i + 1 ? 'bg-purple-500 text-white' : 'bg-gray-100'}`}
              >
                {i + 1}
              </button>
            ))}

            <button
              onClick={() => paginate(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="p-2 disabled:opacity-50"
            >
              <FaAngleRight />
            </button>
          </nav>
        </div>
      )}
    </div>
  );
};

export default PixelPage;